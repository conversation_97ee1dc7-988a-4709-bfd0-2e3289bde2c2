#!/usr/bin/env python3
"""
测试账号检测功能
"""

from utils.account_detector import get_all_account_info, format_account_summary, get_augment_account_info
from utils.paths import get_storage_path, get_db_path

def main():
    print("🔍 测试账号检测功能")
    print("=" * 50)
    
    # 检查文件路径
    storage_path = get_storage_path()
    db_path = get_db_path()
    
    print(f"📁 Storage 文件: {storage_path}")
    print(f"📁 数据库文件: {db_path}")
    
    import os
    print(f"✅ Storage 存在: {os.path.exists(storage_path)}")
    print(f"✅ 数据库存在: {os.path.exists(db_path)}")
    
    print("\n" + "=" * 50)
    
    # 测试AugmentCode账号检测
    print("🔍 检查 AugmentCode 账号信息...")
    try:
        augment_info = get_augment_account_info()
        print(f"登录状态: {augment_info['logged_in']}")
        print(f"邮箱: {augment_info['email']}")
        print(f"用户名: {augment_info['username']}")
        print(f"用户ID: {augment_info['user_id']}")
        print(f"登录时间: {augment_info['login_time']}")
        print(f"Token信息: {len(augment_info['token_info'])} 项")
        print(f"订阅信息: {len(augment_info['subscription_info'])} 项")
        
        if augment_info.get('error'):
            print(f"❌ 错误: {augment_info['error']}")
            
    except Exception as e:
        print(f"❌ AugmentCode账号检测失败: {e}")
    
    print("\n" + "=" * 50)
    
    # 测试完整账号信息
    print("🔍 获取完整账号信息...")
    try:
        account_info = get_all_account_info()
        formatted_info = format_account_summary(account_info)
        print(formatted_info)
        
    except Exception as e:
        print(f"❌ 完整账号信息获取失败: {e}")
    
    print("\n" + "=" * 50)
    
    # 检查storage.json中的关键信息
    print("🔍 分析 storage.json 内容...")
    try:
        import json
        if os.path.exists(storage_path):
            with open(storage_path, 'r', encoding='utf-8') as f:
                storage_data = json.load(f)
            
            print(f"📊 Storage 文件包含 {len(storage_data)} 个键")
            
            # 查找可能包含账号信息的键
            account_keys = []
            for key in storage_data.keys():
                if any(term in key.lower() for term in ['user', 'account', 'email', 'login', 'auth', 'augment']):
                    account_keys.append(key)
            
            print(f"🔍 找到 {len(account_keys)} 个可能相关的键:")
            for key in account_keys[:10]:  # 只显示前10个
                value = storage_data[key]
                if isinstance(value, str) and len(value) > 50:
                    print(f"  - {key}: {value[:50]}...")
                else:
                    print(f"  - {key}: {value}")
                    
        else:
            print("❌ storage.json 文件不存在")
            
    except Exception as e:
        print(f"❌ 分析 storage.json 失败: {e}")

if __name__ == "__main__":
    main()
