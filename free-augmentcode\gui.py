import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, scrolledtext
import threading
import time
import os
from datetime import datetime

# 导入现有的功能模块
from utils.paths import (
    get_home_dir, get_app_data_dir, get_storage_path,
    get_db_path, get_machine_id_path, get_workspace_storage_path, get_extensions_path
)
from utils.extension_checker import (
    get_augment_extension_info, format_extension_info,
    find_augment_related_extensions, check_extension_enabled
)
from utils.account_detector import (
    get_all_account_info, get_augment_account_info,
    detect_augment_activity, format_account_summary
)
from utils.account_manager import (
    get_account_manager, save_login_info, get_current_login_info,
    get_account_subscription_info, format_account_status
)
from utils.cookie_login import quick_validate_login
from augutils.json_modifier import modify_telemetry_ids
from augutils.sqlite_modifier import clean_augment_data
from augutils.workspace_cleaner import clean_workspace_storage

# 设置主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class FreeAugmentCodeGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Free AugmentCode - GUI版本")
        self.root.geometry("1000x800")
        self.root.resizable(True, True)

        # 设置最小窗口大小
        self.root.minsize(800, 600)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 初始化变量
        self.is_running = False
        self.results = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = ctk.CTkLabel(
            self.root, 
            text="🚀 Free AugmentCode", 
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=20)
        
        subtitle_label = ctk.CTkLabel(
            self.root, 
            text="无限续杯 AugmentCode VSCode 插件工具", 
            font=ctk.CTkFont(size=16)
        )
        subtitle_label.pack(pady=(0, 20))
        
        # 创建主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 创建左右分栏
        left_frame = ctk.CTkFrame(main_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)
        
        right_frame = ctk.CTkFrame(main_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)
        
        self.setup_left_panel(left_frame)
        self.setup_right_panel(right_frame)
        
        # 底部状态栏
        self.setup_status_bar()
        
    def setup_left_panel(self, parent):
        """设置左侧面板"""
        # 创建滚动框架来容纳所有内容
        scroll_frame = ctk.CTkScrollableFrame(parent)
        scroll_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 系统路径信息
        paths_label = ctk.CTkLabel(
            scroll_frame,
            text="📁 系统路径信息",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        paths_label.pack(pady=(5, 10))

        # 路径显示框架
        paths_frame = ctk.CTkFrame(scroll_frame)
        paths_frame.pack(fill="x", padx=5, pady=(0, 10))

        self.display_system_paths(paths_frame)

        # AugmentCode扩展信息
        extension_label = ctk.CTkLabel(
            scroll_frame,
            text="🔌 AugmentCode 扩展信息",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        extension_label.pack(pady=(5, 10))

        # 扩展信息显示框架
        extension_frame = ctk.CTkFrame(scroll_frame)
        extension_frame.pack(fill="x", padx=5, pady=(0, 10))

        self.display_extension_info(extension_frame)

        # 账号登录
        login_label = ctk.CTkLabel(
            scroll_frame,
            text="👤 账号登录",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        login_label.pack(pady=(5, 10))

        # 账号登录框架
        login_frame = ctk.CTkFrame(scroll_frame)
        login_frame.pack(fill="x", padx=5, pady=(0, 10))

        self.setup_login_section(login_frame)

        # 当前账号信息
        current_account_label = ctk.CTkLabel(
            scroll_frame,
            text="📊 当前账号状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        current_account_label.pack(pady=(5, 10))

        # 账号信息显示框架
        account_frame = ctk.CTkFrame(scroll_frame)
        account_frame.pack(fill="x", padx=5, pady=(0, 10))

        self.display_account_info(account_frame)

        # 操作控制面板
        control_label = ctk.CTkLabel(
            scroll_frame,
            text="🎮 操作控制",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        control_label.pack(pady=(5, 10))

        # 警告提示
        warning_frame = ctk.CTkFrame(scroll_frame)
        warning_frame.pack(fill="x", padx=5, pady=(0, 10))

        warning_label = ctk.CTkLabel(
            warning_frame,
            text="⚠️ 使用前请确保：\n1. 完全退出 VS Code\n2. 退出 AugmentCode 插件",
            font=ctk.CTkFont(size=11),
            text_color="orange"
        )
        warning_label.pack(pady=8)

        # 操作按钮
        self.start_button = ctk.CTkButton(
            scroll_frame,
            text="🚀 开始清理 AugmentCode 数据",
            font=ctk.CTkFont(size=15, weight="bold"),
            height=45,
            command=self.start_cleaning
        )
        self.start_button.pack(fill="x", padx=5, pady=8)

        # 进度条
        self.progress_label = ctk.CTkLabel(scroll_frame, text="准备就绪", font=ctk.CTkFont(size=11))
        self.progress_label.pack(pady=(8, 3))

        self.progress_bar = ctk.CTkProgressBar(scroll_frame)
        self.progress_bar.pack(fill="x", padx=5, pady=(0, 8))
        self.progress_bar.set(0)
        
    def setup_right_panel(self, parent):
        """设置右侧面板"""
        # 结果显示
        results_label = ctk.CTkLabel(
            parent, 
            text="📊 操作结果", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        results_label.pack(pady=(10, 15))
        
        # 结果显示框架
        self.results_frame = ctk.CTkScrollableFrame(parent, height=200)
        self.results_frame.pack(fill="x", padx=10, pady=(0, 20))
        
        # 日志输出
        log_label = ctk.CTkLabel(
            parent, 
            text="📝 操作日志", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        log_label.pack(pady=(10, 15))
        
        # 日志文本框
        self.log_text = ctk.CTkTextbox(parent, height=250)
        self.log_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # 清空日志按钮
        clear_log_button = ctk.CTkButton(
            parent,
            text="清空日志",
            command=self.clear_log,
            width=100
        )
        clear_log_button.pack(pady=(0, 10))

    def setup_login_section(self, parent):
        """设置登录部分"""
        try:
            # 检查当前登录状态
            account_manager = get_account_manager()
            current_account = account_manager.get_active_account()

            if current_account:
                # 已登录状态
                login_status_label = ctk.CTkLabel(
                    parent,
                    text=f"✅ 已登录: {current_account['email']}",
                    text_color="green",
                    font=ctk.CTkFont(size=12, weight="bold")
                )
                login_status_label.pack(padx=5, pady=5)

                # 登录时间
                time_label = ctk.CTkLabel(
                    parent,
                    text=f"🕒 登录时间: {current_account.get('last_used', '未知')}",
                    font=ctk.CTkFont(size=10)
                )
                time_label.pack(padx=5, pady=(0, 5))

                # 操作按钮
                button_frame = ctk.CTkFrame(parent)
                button_frame.pack(fill="x", padx=5, pady=5)

                logout_button = ctk.CTkButton(
                    button_frame,
                    text="🚪 退出登录",
                    command=self.logout_account,
                    width=100,
                    height=30
                )
                logout_button.pack(side="left", padx=(0, 5))

                change_button = ctk.CTkButton(
                    button_frame,
                    text="🔄 更换账号",
                    command=self.show_login_form,
                    width=100,
                    height=30
                )
                change_button.pack(side="left")

            else:
                # 未登录状态
                self.show_login_form_in_frame(parent)

        except Exception as e:
            error_label = ctk.CTkLabel(
                parent,
                text=f"❌ 登录状态检查失败: {str(e)}",
                text_color="red",
                font=ctk.CTkFont(size=10)
            )
            error_label.pack(padx=5, pady=5)

    def show_login_form_in_frame(self, parent):
        """在指定框架中显示登录表单"""
        # 标题
        title_label = ctk.CTkLabel(
            parent,
            text="🔐 请输入账号信息",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        title_label.pack(padx=5, pady=(5, 10))

        # 邮箱输入
        email_label = ctk.CTkLabel(parent, text="📧 邮箱地址:", font=ctk.CTkFont(size=11))
        email_label.pack(padx=5, pady=(0, 2), anchor="w")

        self.email_entry = ctk.CTkEntry(
            parent,
            placeholder_text="请输入您的邮箱地址",
            width=250,
            height=30
        )
        self.email_entry.pack(padx=5, pady=(0, 10), fill="x")

        # Cookie输入
        cookie_label = ctk.CTkLabel(parent, text="🍪 Cookie信息:", font=ctk.CTkFont(size=11))
        cookie_label.pack(padx=5, pady=(0, 2), anchor="w")

        # 使用Entry而不是Textbox来避免类型错误
        self.cookie_entry = ctk.CTkEntry(
            parent,
            height=100,
            placeholder_text="请粘贴从浏览器复制的Cookie信息（格式：key1=value1; key2=value2）"
        )
        self.cookie_entry.pack(padx=5, pady=(0, 10), fill="x")

        # 添加Cookie格式说明
        cookie_hint = ctk.CTkLabel(
            parent,
            text="📝 Cookie格式示例：session=abc123; auth=def456; user_token=xyz789",
            font=ctk.CTkFont(size=9),
            text_color="gray"
        )
        cookie_hint.pack(padx=5, pady=(0, 5))

        # 提示信息
        hint_label = ctk.CTkLabel(
            parent,
            text="💡 提示：从浏览器开发者工具中复制Cookie",
            font=ctk.CTkFont(size=9),
            text_color="gray"
        )
        hint_label.pack(padx=5, pady=(0, 10))

        # 登录按钮
        login_button = ctk.CTkButton(
            parent,
            text="🚀 登录",
            command=self.perform_login,
            height=35,
            font=ctk.CTkFont(size=12, weight="bold")
        )
        login_button.pack(padx=5, pady=5, fill="x")

    def show_login_form(self):
        """显示登录表单对话框"""
        # 创建新窗口
        login_window = ctk.CTkToplevel(self.root)
        login_window.title("AugmentCode 账号登录")
        login_window.geometry("400x500")
        login_window.resizable(False, False)

        # 设置窗口居中
        login_window.transient(self.root)
        login_window.grab_set()

        # 创建主框架
        main_frame = ctk.CTkFrame(login_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 显示登录表单
        self.show_login_form_in_frame(main_frame)

        # 保存窗口引用
        self.login_window = login_window

    def perform_login(self):
        """执行登录操作"""
        try:
            # 获取输入的信息
            email = self.email_entry.get().strip()
            cookie_string = self.cookie_entry.get().strip()

            # 验证输入
            if not email:
                messagebox.showerror("错误", "请输入邮箱地址！")
                return

            if not cookie_string:
                messagebox.showerror("错误", "请输入Cookie信息！")
                return

            # 验证登录信息
            self.log_message("🔍 正在验证登录信息...", "INFO")
            self.log_message(f"📧 邮箱: {email}", "INFO")
            self.log_message(f"🍪 Cookie长度: {len(cookie_string)} 字符", "INFO")

            success, message = quick_validate_login(email, cookie_string)

            if success:
                # 保存登录信息
                self.log_message("✅ Cookie验证成功，正在保存...", "INFO")
                if save_login_info(email, cookie_string):
                    self.log_message("✅ 登录成功！正在获取订阅信息...", "SUCCESS")

                    # 获取订阅信息
                    sub_success, sub_info = get_account_subscription_info(cookie_string)

                    # 构建成功消息
                    success_msg = f"账号 {email} 登录成功！\n\n"

                    if sub_success:
                        success_msg += "📊 订阅信息:\n"
                        if sub_info.get('plan_name'):
                            success_msg += f"📋 计划: {sub_info['plan_name']}\n"

                        if sub_info.get('usage_limit', 0) > 0:
                            usage_count = sub_info.get('usage_count', 0)
                            usage_limit = sub_info.get('usage_limit', 0)
                            remaining = sub_info.get('remaining_count', 0)
                            usage_percent = (usage_count / usage_limit) * 100 if usage_limit > 0 else 0

                            success_msg += f"🔢 使用情况: {usage_count}/{usage_limit} ({usage_percent:.1f}%)\n"
                            success_msg += f"⚡ 剩余次数: {remaining} 次\n"
                        else:
                            success_msg += f"🔢 已使用: {sub_info.get('usage_count', 0)} 次\n"

                        if sub_info.get('reset_date'):
                            success_msg += f"🔄 重置日期: {sub_info['reset_date']}\n"

                        self.log_message(f"📊 订阅信息获取成功: {sub_info.get('plan_name', 'Unknown')}", "INFO")
                    else:
                        success_msg += f"⚠️ 订阅信息: {sub_info.get('error', '获取失败')}\n"
                        self.log_message(f"⚠️ 订阅信息获取失败: {sub_info.get('error', '未知错误')}", "WARNING")

                    success_msg += "\nCookie信息已安全保存，下次启动将自动加载。"

                    messagebox.showinfo("登录成功", success_msg)

                    # 关闭登录窗口（如果存在）
                    if hasattr(self, 'login_window'):
                        self.login_window.destroy()

                    # 刷新界面
                    self.refresh_all_info()
                else:
                    self.log_message("❌ 保存登录信息失败", "ERROR")
                    messagebox.showerror("保存失败", "Cookie验证成功，但保存登录信息失败！\n请检查文件权限。")
            else:
                self.log_message(f"❌ 登录验证失败: {message}", "ERROR")

                # 提供更详细的错误信息和解决方案
                error_details = f"登录验证失败：{message}\n\n"
                error_details += "可能的解决方案：\n"
                error_details += "1. 检查邮箱地址是否正确\n"
                error_details += "2. 确保Cookie格式正确\n"
                error_details += "3. 重新从浏览器获取最新Cookie\n"
                error_details += "4. 确保Cookie未过期"

                messagebox.showerror("登录失败", error_details)

        except Exception as e:
            error_msg = f"登录过程出错: {str(e)}"
            self.log_message(f"❌ {error_msg}", "ERROR")
            messagebox.showerror("错误", error_msg)

    def logout_account(self):
        """退出登录"""
        try:
            # 确认退出
            if messagebox.askyesno("确认", "确定要退出当前账号吗？"):
                account_manager = get_account_manager()
                account_manager.clear_all_accounts()

                self.log_message("🚪 已退出登录", "INFO")
                messagebox.showinfo("成功", "已成功退出登录！")

                # 刷新界面
                self.refresh_all_info()

        except Exception as e:
            error_msg = f"退出登录失败: {str(e)}"
            self.log_message(f"❌ {error_msg}", "ERROR")
            messagebox.showerror("错误", error_msg)

    def refresh_all_info(self):
        """刷新所有信息"""
        try:
            # 刷新登录部分
            for widget in self.root.winfo_children():
                if isinstance(widget, ctk.CTkFrame):
                    for child in widget.winfo_children():
                        if isinstance(child, ctk.CTkScrollableFrame):
                            for section in child.winfo_children():
                                if isinstance(section, ctk.CTkFrame):
                                    # 清空并重新创建内容
                                    for item in section.winfo_children():
                                        item.destroy()

                                    # 重新设置内容
                                    if "登录" in str(section):
                                        self.setup_login_section(section)
                                    elif "账号" in str(section):
                                        self.display_account_info(section)

            self.log_message("🔄 界面信息已刷新", "INFO")

        except Exception as e:
            self.log_message(f"❌ 刷新界面失败: {str(e)}", "ERROR")
        
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_frame = ctk.CTkFrame(self.root, height=30)
        self.status_frame.pack(fill="x", side="bottom", padx=20, pady=(0, 10))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame, 
            text="状态：准备就绪", 
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # 版本信息
        version_label = ctk.CTkLabel(
            self.status_frame, 
            text="v1.0.0 | MIT License", 
            font=ctk.CTkFont(size=10)
        )
        version_label.pack(side="right", padx=10, pady=5)
        
    def display_system_paths(self, parent):
        """显示系统路径信息"""
        paths = {
            "用户目录": get_home_dir(),
            "应用数据目录": get_app_data_dir(),
            "存储文件": get_storage_path(),
            "数据库文件": get_db_path(),
            "机器ID文件": get_machine_id_path(),
            "工作区存储": get_workspace_storage_path()
        }
        
        for name, path in paths.items():
            path_frame = ctk.CTkFrame(parent)
            path_frame.pack(fill="x", pady=2)
            
            name_label = ctk.CTkLabel(
                path_frame, 
                text=f"{name}:", 
                font=ctk.CTkFont(weight="bold"),
                width=100
            )
            name_label.pack(side="left", padx=(10, 5), pady=5)
            
            # 检查文件是否存在
            exists = os.path.exists(path)
            status_color = "green" if exists else "red"
            status_text = "✓" if exists else "✗"
            
            status_label = ctk.CTkLabel(
                path_frame, 
                text=status_text, 
                text_color=status_color,
                width=20
            )
            status_label.pack(side="left", padx=5, pady=5)
            
            path_label = ctk.CTkLabel(
                path_frame, 
                text=path, 
                font=ctk.CTkFont(size=10)
            )
            path_label.pack(side="left", padx=5, pady=5, fill="x", expand=True)

    def display_extension_info(self, parent):
        """显示AugmentCode扩展信息"""
        try:
            ext_info = get_augment_extension_info()

            if ext_info['installed']:
                # 扩展已安装 - 简化显示
                status_color = "green" if ext_info['enabled'] else "orange"
                status_text = "✅ 已安装并启用" if ext_info['enabled'] else "⚠️ 已安装但禁用"

                # 状态信息
                status_label = ctk.CTkLabel(
                    parent,
                    text=f"{status_text} - {ext_info['display_name']} v{ext_info['version']}",
                    text_color=status_color,
                    font=ctk.CTkFont(size=12, weight="bold")
                )
                status_label.pack(padx=5, pady=5)

                # 详细信息（单行）
                details_label = ctk.CTkLabel(
                    parent,
                    text=f"📅 {ext_info['install_date']} | 📊 {ext_info['size']} | 👤 {ext_info['publisher']}",
                    font=ctk.CTkFont(size=10)
                )
                details_label.pack(padx=5, pady=(0, 5))

            else:
                # 扩展未安装
                no_ext_label = ctk.CTkLabel(
                    parent,
                    text="❌ AugmentCode 扩展未安装",
                    text_color="red",
                    font=ctk.CTkFont(size=12, weight="bold")
                )
                no_ext_label.pack(padx=5, pady=5)

                hint_label = ctk.CTkLabel(
                    parent,
                    text="请先在 VS Code 中安装 AugmentCode 扩展",
                    font=ctk.CTkFont(size=10)
                )
                hint_label.pack(padx=5, pady=(0, 5))

            # 刷新按钮
            refresh_button = ctk.CTkButton(
                parent,
                text="🔄 刷新",
                command=lambda: self.refresh_extension_info(parent),
                width=80,
                height=25
            )
            refresh_button.pack(pady=3)

        except Exception as e:
            error_label = ctk.CTkLabel(
                parent,
                text=f"❌ 获取扩展信息失败: {str(e)}",
                text_color="red",
                font=ctk.CTkFont(size=10)
            )
            error_label.pack(padx=5, pady=5)

    def refresh_extension_info(self, parent):
        """刷新扩展信息"""
        # 清空现有内容
        for widget in parent.winfo_children():
            widget.destroy()

        # 重新显示扩展信息
        self.display_extension_info(parent)
        self.log_message("扩展信息已刷新", "INFO")

    def display_account_info(self, parent):
        """显示账号信息"""
        try:
            # 首先尝试获取我们自己管理的账号信息
            current_login = get_current_login_info()

            if current_login:
                # 显示登录状态
                status_label = ctk.CTkLabel(
                    parent,
                    text="🔐 状态: ✅ 已登录 (Cookie)",
                    text_color="green",
                    font=ctk.CTkFont(size=12, weight="bold")
                )
                status_label.pack(padx=5, pady=5)

                # 显示邮箱
                email_label = ctk.CTkLabel(
                    parent,
                    text=f"📧 邮箱: {current_login['email']}",
                    font=ctk.CTkFont(size=11)
                )
                email_label.pack(padx=5, pady=(0, 3))

                # 显示用户名
                user_label = ctk.CTkLabel(
                    parent,
                    text=f"👤 用户: {current_login['username']}",
                    font=ctk.CTkFont(size=11)
                )
                user_label.pack(padx=5, pady=(0, 3))

                # 显示登录时间
                time_label = ctk.CTkLabel(
                    parent,
                    text=f"🕒 时间: {current_login['last_used']}",
                    font=ctk.CTkFont(size=10)
                )
                time_label.pack(padx=5, pady=(0, 3))

                # 显示Cookie信息
                cookie_length = len(current_login.get('cookie_string', ''))
                cookie_label = ctk.CTkLabel(
                    parent,
                    text=f"🍪 Cookie: {cookie_length} 字符",
                    font=ctk.CTkFont(size=10),
                    text_color="gray"
                )
                cookie_label.pack(padx=5, pady=(0, 5))

                # 获取并显示订阅信息
                try:
                    sub_success, sub_info = get_account_subscription_info()

                    if sub_success:
                        # 创建订阅信息框架
                        sub_frame = ctk.CTkFrame(parent)
                        sub_frame.pack(fill="x", padx=5, pady=(5, 0))

                        # 订阅标题
                        sub_title = ctk.CTkLabel(
                            sub_frame,
                            text="📊 订阅信息",
                            font=ctk.CTkFont(size=11, weight="bold"),
                            text_color="orange"
                        )
                        sub_title.pack(padx=5, pady=(5, 2))

                        # 计划名称
                        if sub_info.get('plan_name'):
                            plan_label = ctk.CTkLabel(
                                sub_frame,
                                text=f"📋 计划: {sub_info['plan_name']}",
                                font=ctk.CTkFont(size=10)
                            )
                            plan_label.pack(padx=5, pady=(0, 2))

                        # 使用情况
                        if sub_info.get('usage_limit', 0) > 0:
                            usage_count = sub_info.get('usage_count', 0)
                            usage_limit = sub_info.get('usage_limit', 0)
                            remaining = sub_info.get('remaining_count', 0)
                            usage_percent = (usage_count / usage_limit) * 100 if usage_limit > 0 else 0

                            usage_label = ctk.CTkLabel(
                                sub_frame,
                                text=f"🔢 使用: {usage_count}/{usage_limit} ({usage_percent:.1f}%)",
                                font=ctk.CTkFont(size=10)
                            )
                            usage_label.pack(padx=5, pady=(0, 2))

                            # 剩余次数 - 根据剩余量设置颜色
                            if remaining > 50:
                                remaining_color = "green"
                            elif remaining > 10:
                                remaining_color = "orange"
                            else:
                                remaining_color = "red"

                            remaining_label = ctk.CTkLabel(
                                sub_frame,
                                text=f"⚡ 剩余: {remaining} 次",
                                font=ctk.CTkFont(size=10, weight="bold"),
                                text_color=remaining_color
                            )
                            remaining_label.pack(padx=5, pady=(0, 2))
                        else:
                            usage_label = ctk.CTkLabel(
                                sub_frame,
                                text=f"🔢 已使用: {sub_info.get('usage_count', 0)} 次",
                                font=ctk.CTkFont(size=10)
                            )
                            usage_label.pack(padx=5, pady=(0, 2))

                        # 重置日期
                        if sub_info.get('reset_date'):
                            reset_label = ctk.CTkLabel(
                                sub_frame,
                                text=f"🔄 重置: {sub_info['reset_date']}",
                                font=ctk.CTkFont(size=9),
                                text_color="gray"
                            )
                            reset_label.pack(padx=5, pady=(0, 5))
                    else:
                        # 订阅信息获取失败
                        sub_error_label = ctk.CTkLabel(
                            parent,
                            text=f"⚠️ 订阅信息: {sub_info.get('error', '获取失败')}",
                            font=ctk.CTkFont(size=10),
                            text_color="orange"
                        )
                        sub_error_label.pack(padx=5, pady=(5, 0))

                except Exception as e:
                    # 订阅信息查询异常
                    sub_error_label = ctk.CTkLabel(
                        parent,
                        text=f"⚠️ 订阅查询异常: {str(e)[:30]}...",
                        font=ctk.CTkFont(size=9),
                        text_color="red"
                    )
                    sub_error_label.pack(padx=5, pady=(5, 0))

            else:
                # 尝试获取系统检测的账号信息作为备用
                account_info = get_all_account_info()
                augment_account = account_info['augment_account']
                augment_activity = account_info['augment_activity']

                if augment_account['logged_in']:
                    # 显示系统检测到的登录状态
                    login_method = augment_account.get('login_method', 'unknown')
                    if login_method == 'cookie':
                        status_text = "✅ 已登录 (Cookie)"
                        status_color = "green"
                    else:
                        status_text = "✅ 已登录"
                        status_color = "green"

                    status_label = ctk.CTkLabel(
                        parent,
                        text=f"🔐 状态: {status_text}",
                        text_color=status_color,
                        font=ctk.CTkFont(size=12, weight="bold")
                    )
                    status_label.pack(padx=5, pady=5)

                    # 显示邮箱
                    if augment_account['email']:
                        email_label = ctk.CTkLabel(
                            parent,
                            text=f"📧 邮箱: {augment_account['email']}",
                            font=ctk.CTkFont(size=11)
                        )
                        email_label.pack(padx=5, pady=(0, 3))

                    # 显示用户名
                    if augment_account['username']:
                        user_label = ctk.CTkLabel(
                            parent,
                            text=f"👤 用户: {augment_account['username']}",
                            font=ctk.CTkFont(size=11)
                        )
                        user_label.pack(padx=5, pady=(0, 3))

                    # 显示登录时间
                    if augment_account['login_time']:
                        time_label = ctk.CTkLabel(
                            parent,
                            text=f"🕒 时间: {augment_account['login_time']}",
                            font=ctk.CTkFont(size=10)
                        )
                        time_label.pack(padx=5, pady=(0, 3))

                    # 显示Cookie信息
                    cookie_info = augment_account.get('cookie_info', {})
                    if cookie_info.get('has_cookie'):
                        cookie_label = ctk.CTkLabel(
                            parent,
                            text=f"🍪 Cookie: {cookie_info['cookie_length']} 字符",
                            font=ctk.CTkFont(size=10),
                            text_color="gray"
                        )
                        cookie_label.pack(padx=5, pady=(0, 5))
                else:
                    # 未登录状态
                    no_login_label = ctk.CTkLabel(
                        parent,
                        text="🔐 状态: ❌ 未登录",
                        text_color="red",
                        font=ctk.CTkFont(size=12, weight="bold")
                    )
                    no_login_label.pack(padx=5, pady=5)

                    hint_label = ctk.CTkLabel(
                        parent,
                        text="请在上方登录部分输入账号信息",
                        font=ctk.CTkFont(size=10),
                        text_color="gray"
                    )
                    hint_label.pack(padx=5, pady=(0, 5))

            # 显示活动状态（如果有）
            if 'augment_activity' in locals() and augment_activity.get('is_active'):
                activity_indicators = []
                if augment_activity.get('extension_confirmed'):
                    activity_indicators.append("扩展确认")
                if augment_activity.get('publisher_trusted'):
                    activity_indicators.append("发布者信任")
                if augment_activity.get('chat_history'):
                    activity_indicators.append("聊天使用")
                if augment_activity.get('webview_sessions'):
                    activity_indicators.append(f"会话({len(augment_activity['webview_sessions'])})")

                if activity_indicators:
                    activity_label = ctk.CTkLabel(
                        parent,
                        text=f"📊 活动: {', '.join(activity_indicators)}",
                        font=ctk.CTkFont(size=10),
                        text_color="orange"
                    )
                    activity_label.pack(padx=5, pady=(0, 5))

            # 刷新按钮
            refresh_button = ctk.CTkButton(
                parent,
                text="🔄 刷新状态",
                command=lambda: self.refresh_account_info(parent),
                width=100,
                height=25
            )
            refresh_button.pack(pady=5)

        except Exception as e:
            error_label = ctk.CTkLabel(
                parent,
                text=f"❌ 获取账号信息失败: {str(e)}",
                text_color="red",
                font=ctk.CTkFont(size=10)
            )
            error_label.pack(padx=5, pady=5)

    def refresh_account_info(self, parent):
        """刷新账号信息"""
        # 清空现有内容
        for widget in parent.winfo_children():
            widget.destroy()

        # 重新显示账号信息
        self.display_account_info(parent)
        self.log_message("账号信息已刷新", "INFO")

    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete("1.0", "end")
        
    def update_status(self, message):
        """更新状态栏"""
        self.status_label.configure(text=f"状态：{message}")
        self.root.update_idletasks()
        
    def update_progress(self, value, message=""):
        """更新进度条"""
        self.progress_bar.set(value)
        if message:
            self.progress_label.configure(text=message)
        self.root.update_idletasks()

    def start_cleaning(self):
        """开始清理操作"""
        if self.is_running:
            messagebox.showwarning("警告", "操作正在进行中，请等待完成！")
            return

        # 检查扩展和账号状态
        ext_info = get_augment_extension_info()
        account_info = get_all_account_info()

        warning_messages = []

        if ext_info['installed'] and ext_info['enabled']:
            warning_messages.append(
                f"🔌 检测到 AugmentCode 扩展仍在运行！\n"
                f"• 名称: {ext_info['display_name']}\n"
                f"• 版本: {ext_info['version']}\n"
                f"• 状态: 启用"
            )

        if account_info['augment_account']['logged_in'] or account_info['augment_activity']['is_active']:
            if account_info['augment_account']['logged_in']:
                email_info = f" ({account_info['augment_account']['email']})" if account_info['augment_account']['email'] else ""
                warning_messages.append(f"👤 检测到 AugmentCode 账号已登录{email_info}")

            if account_info['augment_activity']['is_active']:
                activity_count = len(account_info['augment_activity']['recent_activity'])
                warning_messages.append(f"🚀 检测到 AugmentCode 活跃状态 ({activity_count} 项活动记录)")

        if warning_messages:
            full_warning = "\n\n".join(warning_messages)
            full_warning += "\n\n请先完全退出 VS Code 再继续操作。\n点击确定继续，或取消操作。"

            messagebox.showwarning("警告", full_warning)

        # 确认对话框
        result = messagebox.askyesno(
            "确认操作",
            "此操作将：\n"
            "1. 检查 AugmentCode 扩展状态\n"
            "2. 修改 VS Code 的设备和机器 ID\n"
            "3. 清理 SQLite 数据库中的 AugmentCode 记录\n"
            "4. 清理工作区存储文件\n\n"
            "所有原始数据将被备份。\n\n"
            "确定要继续吗？"
        )

        if not result:
            return

        # 在新线程中执行清理操作
        self.is_running = True
        self.start_button.configure(state="disabled", text="正在清理...")

        thread = threading.Thread(target=self.perform_cleaning)
        thread.daemon = True
        thread.start()

    def perform_cleaning(self):
        """执行清理操作（在后台线程中运行）"""
        try:
            self.log_message("开始 AugmentCode 数据清理操作", "INFO")
            self.update_status("正在清理...")
            self.update_progress(0, "准备开始...")

            # 步骤0：检查扩展和账号状态
            self.log_message("步骤 0/5: 检查 AugmentCode 状态", "INFO")
            self.update_progress(0.05, "检查状态...")

            try:
                # 检查扩展状态
                ext_info = get_augment_extension_info()
                if ext_info['installed']:
                    self.log_message(f"✓ 发现 AugmentCode 扩展: {ext_info['display_name']} v{ext_info['version']}", "INFO")
                    self.log_message(f"✓ 发布者: {ext_info['publisher']}", "INFO")
                    self.log_message(f"✓ 安装时间: {ext_info['install_date']}", "INFO")
                    self.log_message(f"✓ 扩展大小: {ext_info['size']}", "INFO")

                    if ext_info['enabled']:
                        self.log_message("⚠️ 警告: 扩展当前处于启用状态", "WARNING")
                        self.log_message("⚠️ 建议完全退出 VS Code 以确保清理效果", "WARNING")
                    else:
                        self.log_message("✓ 扩展当前处于禁用状态", "SUCCESS")
                else:
                    self.log_message("ℹ️ 未检测到 AugmentCode 扩展", "INFO")

                # 检查账号状态
                account_info = get_all_account_info()
                augment_account = account_info['augment_account']
                augment_activity = account_info['augment_activity']

                if augment_account['logged_in']:
                    email_info = f" ({augment_account['email']})" if augment_account['email'] else ""
                    self.log_message(f"👤 检测到登录账号{email_info}", "INFO")
                    if augment_account['username']:
                        self.log_message(f"✓ 用户名: {augment_account['username']}", "INFO")
                else:
                    self.log_message("👤 未检测到明确的登录信息", "INFO")

                if augment_activity['is_active']:
                    activity_count = len(augment_activity['recent_activity'])
                    self.log_message(f"🚀 检测到活跃状态: {activity_count} 项活动记录", "INFO")

                    activity_details = []
                    if augment_activity['extension_confirmed']:
                        activity_details.append("扩展已确认")
                    if augment_activity['publisher_trusted']:
                        activity_details.append("发布者已信任")
                    if augment_activity['chat_history']:
                        activity_details.append("聊天功能已使用")
                    if augment_activity['webview_sessions']:
                        activity_details.append(f"Webview会话({len(augment_activity['webview_sessions'])}个)")

                    if activity_details:
                        self.log_message(f"📊 活动指标: {', '.join(activity_details)}", "INFO")
                else:
                    self.log_message("🚀 未检测到明显的活跃状态", "INFO")

                self.update_progress(0.1, "状态检查完成")

            except Exception as e:
                self.log_message(f"⚠️ 状态检查失败: {str(e)}", "WARNING")
                self.log_message("继续执行清理操作...", "INFO")

            time.sleep(0.5)

            # 步骤1：修改 Telemetry IDs
            self.log_message("步骤 1/5: 修改 Telemetry IDs", "INFO")
            self.update_progress(0.2, "修改 Telemetry IDs...")

            try:
                result = modify_telemetry_ids()
                self.results['telemetry'] = result

                self.log_message(f"✓ 存储备份已创建: {result['storage_backup_path']}", "SUCCESS")
                if result['machine_id_backup_path']:
                    self.log_message(f"✓ 机器ID备份已创建: {result['machine_id_backup_path']}", "SUCCESS")

                self.log_message(f"✓ 旧机器ID: {result['old_machine_id'][:32]}...", "INFO")
                self.log_message(f"✓ 新机器ID: {result['new_machine_id'][:32]}...", "INFO")
                self.log_message(f"✓ 旧设备ID: {result['old_device_id']}", "INFO")
                self.log_message(f"✓ 新设备ID: {result['new_device_id']}", "INFO")

                self.update_progress(0.35, "Telemetry IDs 修改完成")

            except Exception as e:
                self.log_message(f"✗ Telemetry IDs 修改失败: {str(e)}", "ERROR")
                raise e

            time.sleep(0.5)  # 短暂延迟以显示进度

            # 步骤2：清理 SQLite 数据库
            self.log_message("步骤 2/5: 清理 SQLite 数据库", "INFO")
            self.update_progress(0.5, "清理数据库...")

            try:
                db_result = clean_augment_data()
                self.results['database'] = db_result

                self.log_message(f"✓ 数据库备份已创建: {db_result['db_backup_path']}", "SUCCESS")
                self.log_message(f"✓ 已删除 {db_result['deleted_rows']} 条包含 'augment' 的记录", "SUCCESS")

                self.update_progress(0.65, "数据库清理完成")

            except Exception as e:
                self.log_message(f"✗ 数据库清理失败: {str(e)}", "ERROR")
                raise e

            time.sleep(0.5)

            # 步骤3：清理工作区存储
            self.log_message("步骤 3/5: 清理工作区存储", "INFO")
            self.update_progress(0.75, "清理工作区存储...")

            try:
                ws_result = clean_workspace_storage()
                self.results['workspace'] = ws_result

                self.log_message(f"✓ 工作区备份已创建: {ws_result['backup_path']}", "SUCCESS")
                self.log_message(f"✓ 已删除 {ws_result['deleted_files_count']} 个工作区文件", "SUCCESS")

                if ws_result.get('failed_operations'):
                    self.log_message(f"⚠ 有 {len(ws_result['failed_operations'])} 个文件删除失败", "WARNING")

                self.update_progress(0.85, "工作区清理完成")

            except Exception as e:
                self.log_message(f"✗ 工作区清理失败: {str(e)}", "ERROR")
                raise e

            time.sleep(0.5)

            # 步骤4：最终检查
            self.log_message("步骤 4/5: 最终检查", "INFO")
            self.update_progress(0.9, "执行最终检查...")

            try:
                # 再次检查扩展状态
                final_ext_info = get_augment_extension_info()
                if final_ext_info['installed']:
                    self.log_message(f"✓ 扩展仍然存在: {final_ext_info['display_name']} v{final_ext_info['version']}", "INFO")
                    self.log_message("ℹ️ 这是正常的，扩展文件不会被删除", "INFO")
                else:
                    self.log_message("ℹ️ 扩展信息检查完成", "INFO")

                # 再次检查账号状态
                final_account_info = get_all_account_info()
                if final_account_info['augment_activity']['is_active']:
                    remaining_activities = len(final_account_info['augment_activity']['recent_activity'])
                    self.log_message(f"ℹ️ 清理后仍检测到 {remaining_activities} 项活动记录", "INFO")
                    self.log_message("ℹ️ 这可能是正常的，某些记录可能需要重启VS Code后才会清除", "INFO")
                else:
                    self.log_message("✓ 账号活动状态已清理", "SUCCESS")

                self.update_progress(0.95, "最终检查完成")

            except Exception as e:
                self.log_message(f"⚠️ 最终检查失败: {str(e)}", "WARNING")

            # 步骤5：完成
            self.log_message("步骤 5/5: 操作完成", "INFO")
            self.update_progress(1.0, "所有操作完成")

            # 完成
            self.log_message("🎉 所有清理操作已完成！", "SUCCESS")
            self.log_message("现在可以重新启动 VS Code 并使用新邮箱登录 AugmentCode", "INFO")
            self.update_status("清理完成")

            # 显示结果
            self.root.after(0, self.display_results)

            # 显示完成对话框
            self.root.after(0, lambda: messagebox.showinfo(
                "操作完成",
                "AugmentCode 数据清理完成！\n\n"
                "现在可以：\n"
                "1. 重新启动 VS Code\n"
                "2. 使用新的邮箱登录 AugmentCode 插件\n\n"
                "所有原始数据已备份，请查看操作日志了解详情。"
            ))

        except FileNotFoundError as e:
            self.log_message(f"✗ 文件未找到: {str(e)}", "ERROR")
            self.log_message("请确保 VS Code 已安装且至少运行过一次", "ERROR")
            self.root.after(0, lambda: messagebox.showerror("错误", f"文件未找到：{str(e)}\n\n请确保 VS Code 已安装且至少运行过一次。"))

        except Exception as e:
            self.log_message(f"✗ 操作失败: {str(e)}", "ERROR")
            self.root.after(0, lambda: messagebox.showerror("错误", f"操作失败：{str(e)}"))

        finally:
            # 重置UI状态
            self.is_running = False
            self.root.after(0, lambda: self.start_button.configure(state="normal", text="🚀 开始清理 AugmentCode 数据"))
            self.root.after(0, lambda: self.update_status("准备就绪"))

    def display_results(self):
        """显示操作结果"""
        # 清空之前的结果
        for widget in self.results_frame.winfo_children():
            widget.destroy()

        if not self.results:
            no_results_label = ctk.CTkLabel(
                self.results_frame,
                text="暂无操作结果",
                font=ctk.CTkFont(size=12)
            )
            no_results_label.pack(pady=10)
            return

        # 显示扩展信息
        try:
            ext_info = get_augment_extension_info()
            if ext_info['installed']:
                self.create_result_section("🔌 AugmentCode 扩展", ext_info, [
                    ("扩展名称", "display_name", False),
                    ("版本", "version", False),
                    ("发布者", "publisher", False),
                    ("安装时间", "install_date", False),
                    ("扩展大小", "size", False),
                    ("状态", "enabled", False, lambda x: "✅ 启用" if x else "⚠️ 禁用")
                ])
        except Exception as e:
            self.log_message(f"显示扩展信息失败: {str(e)}", "WARNING")

        # 显示 Telemetry 结果
        if 'telemetry' in self.results:
            self.create_result_section("📝 Telemetry IDs", self.results['telemetry'], [
                ("旧机器ID", "old_machine_id", True),
                ("新机器ID", "new_machine_id", True),
                ("旧设备ID", "old_device_id", False),
                ("新设备ID", "new_device_id", False),
                ("存储备份", "storage_backup_path", False),
                ("机器ID备份", "machine_id_backup_path", False)
            ])

        # 显示数据库结果
        if 'database' in self.results:
            self.create_result_section("🗃️ 数据库清理", self.results['database'], [
                ("数据库备份", "db_backup_path", False),
                ("删除记录数", "deleted_rows", False)
            ])

        # 显示工作区结果
        if 'workspace' in self.results:
            self.create_result_section("💾 工作区清理", self.results['workspace'], [
                ("工作区备份", "backup_path", False),
                ("删除文件数", "deleted_files_count", False)
            ])

    def create_result_section(self, title, data, fields):
        """创建结果显示区域"""
        # 标题
        title_label = ctk.CTkLabel(
            self.results_frame,
            text=title,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # 结果框架
        section_frame = ctk.CTkFrame(self.results_frame)
        section_frame.pack(fill="x", padx=10, pady=(0, 10))

        for field_info in fields:
            if len(field_info) == 3:
                field_name, field_key, truncate = field_info
                formatter = None
            elif len(field_info) == 4:
                field_name, field_key, truncate, formatter = field_info
            else:
                continue

            if field_key in data and data[field_key] is not None:
                if formatter:
                    value = formatter(data[field_key])
                else:
                    value = str(data[field_key])

                if truncate and len(value) > 32:
                    value = value[:32] + "..."

                field_frame = ctk.CTkFrame(section_frame)
                field_frame.pack(fill="x", padx=5, pady=2)

                name_label = ctk.CTkLabel(
                    field_frame,
                    text=f"{field_name}:",
                    font=ctk.CTkFont(weight="bold"),
                    width=80
                )
                name_label.pack(side="left", padx=(5, 2), pady=2)

                value_label = ctk.CTkLabel(
                    field_frame,
                    text=value,
                    font=ctk.CTkFont(size=10)
                )
                value_label.pack(side="left", padx=2, pady=2, fill="x", expand=True)

    def run(self):
        """运行GUI应用"""
        self.log_message("Free AugmentCode GUI 已启动", "INFO")
        self.log_message("请在使用前确保完全退出 VS Code 和 AugmentCode 插件", "WARNING")
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = FreeAugmentCodeGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("启动错误", f"GUI 启动失败：{str(e)}")

if __name__ == "__main__":
    main()
