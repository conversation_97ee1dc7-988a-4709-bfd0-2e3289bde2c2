#!/usr/bin/env python3
"""
Cookie登录功能模块
处理用户输入的账号和Cookie信息进行登录验证
"""

import json
import os
import requests
import time
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta

class CookieLoginManager:
    """Cookie登录管理器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.login_info = {}
        
    def validate_cookie_format(self, cookie_string: str) -> bool:
        """
        验证Cookie格式是否正确
        
        Args:
            cookie_string: Cookie字符串
            
        Returns:
            bool: Cookie格式是否有效
        """
        if not cookie_string or not isinstance(cookie_string, str):
            return False
            
        # 检查基本格式 (key=value; key2=value2)
        try:
            # 简单的Cookie格式验证
            parts = cookie_string.split(';')
            for part in parts:
                if '=' not in part.strip():
                    return False
            return True
        except:
            return False
    
    def parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """
        解析Cookie字符串为字典
        
        Args:
            cookie_string: Cookie字符串
            
        Returns:
            dict: 解析后的Cookie字典
        """
        cookies = {}
        if not cookie_string:
            return cookies
            
        try:
            parts = cookie_string.split(';')
            for part in parts:
                if '=' in part:
                    key, value = part.strip().split('=', 1)
                    cookies[key.strip()] = value.strip()
        except Exception as e:
            print(f"解析Cookie失败: {e}")
            
        return cookies
    
    def validate_augment_login(self, email: str, cookie_string: str) -> Tuple[bool, str, Dict]:
        """
        验证AugmentCode登录信息
        
        Args:
            email: 用户邮箱
            cookie_string: Cookie字符串
            
        Returns:
            tuple: (是否成功, 消息, 用户信息)
        """
        try:
            # 验证邮箱格式
            if not email or '@' not in email:
                return False, "邮箱格式无效", {}
            
            # 验证Cookie格式
            if not self.validate_cookie_format(cookie_string):
                return False, "Cookie格式无效", {}
            
            # 解析Cookie
            cookies = self.parse_cookie_string(cookie_string)
            if not cookies:
                return False, "Cookie解析失败", {}
            
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': 'https://augmentcode.com/',
                'Origin': 'https://augmentcode.com'
            }
            
            # 设置Cookie
            for key, value in cookies.items():
                self.session.cookies.set(key, value)
            
            # 模拟验证请求（这里可以根据实际的AugmentCode API调整）
            # 由于我们无法访问真实的AugmentCode API，这里进行基本验证
            
            # 检查关键Cookie是否存在
            required_cookies = ['session', 'auth', 'token', 'user']
            has_required = any(key.lower() in [c.lower() for c in cookies.keys()] for key in required_cookies)
            
            if not has_required:
                return False, "Cookie中缺少必要的认证信息", {}
            
            # 构造用户信息
            user_info = {
                'email': email,
                'username': email.split('@')[0],  # 从邮箱提取用户名
                'login_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'cookie_count': len(cookies),
                'cookies': cookies,
                'validated': True
            }
            
            return True, "登录验证成功", user_info
            
        except Exception as e:
            return False, f"验证过程出错: {str(e)}", {}
    
    def test_cookie_validity(self, cookies: Dict[str, str]) -> bool:
        """
        测试Cookie是否仍然有效
        
        Args:
            cookies: Cookie字典
            
        Returns:
            bool: Cookie是否有效
        """
        try:
            # 这里可以实现实际的Cookie有效性测试
            # 目前返回True表示假设Cookie有效
            return len(cookies) > 0
        except:
            return False
    
    def format_cookie_info(self, cookies: Dict[str, str]) -> str:
        """
        格式化Cookie信息用于显示
        
        Args:
            cookies: Cookie字典
            
        Returns:
            str: 格式化的Cookie信息
        """
        if not cookies:
            return "无Cookie信息"
        
        info_lines = []
        for key, value in cookies.items():
            # 隐藏敏感信息
            if len(value) > 20:
                display_value = value[:10] + "..." + value[-5:]
            else:
                display_value = value
            info_lines.append(f"  {key}: {display_value}")
        
        return f"Cookie信息 ({len(cookies)} 项):\n" + "\n".join(info_lines)


def create_cookie_login_manager() -> CookieLoginManager:
    """创建Cookie登录管理器实例"""
    return CookieLoginManager()


def quick_validate_login(email: str, cookie_string: str) -> Tuple[bool, str]:
    """
    快速验证登录信息
    
    Args:
        email: 邮箱
        cookie_string: Cookie字符串
        
    Returns:
        tuple: (是否成功, 消息)
    """
    manager = create_cookie_login_manager()
    success, message, _ = manager.validate_augment_login(email, cookie_string)
    return success, message
