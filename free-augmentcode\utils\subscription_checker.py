#!/usr/bin/env python3
"""
订阅信息检查模块
查询AugmentCode账号的使用次数和订阅状态
"""

import requests
import json
from typing import Dict, Optional, Tuple
from datetime import datetime
import urllib.parse

class SubscriptionChecker:
    """订阅信息检查器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://app.augmentcode.com"
        
    def setup_session(self, cookies: Dict[str, str]):
        """
        设置会话Cookie
        
        Args:
            cookies: Cookie字典
        """
        for key, value in cookies.items():
            self.session.cookies.set(key, value)
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Referer': 'https://app.augmentcode.com/',
            'Origin': 'https://app.augmentcode.com'
        })
    
    def get_subscription_info(self, cookie_string: str) -> Dict:
        """
        获取订阅信息
        
        Args:
            cookie_string: Cookie字符串
            
        Returns:
            dict: 订阅信息
        """
        result = {
            'success': False,
            'plan_name': '',
            'usage_count': 0,
            'usage_limit': 0,
            'remaining_count': 0,
            'reset_date': '',
            'subscription_status': '',
            'error': ''
        }
        
        try:
            # 解析Cookie
            cookies = self._parse_cookie_string(cookie_string)
            if not cookies:
                result['error'] = 'Cookie解析失败'
                return result
            
            # 设置会话
            self.setup_session(cookies)
            
            # 尝试获取订阅信息
            subscription_data = self._fetch_subscription_data()
            if subscription_data:
                result.update(subscription_data)
                result['success'] = True
            else:
                # 如果API调用失败，尝试从页面解析
                page_data = self._fetch_from_page()
                if page_data:
                    result.update(page_data)
                    result['success'] = True
                else:
                    result['error'] = '无法获取订阅信息'
            
        except Exception as e:
            result['error'] = f'查询过程出错: {str(e)}'
        
        return result
    
    def _parse_cookie_string(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        
        try:
            if ';' in cookie_string:
                parts = cookie_string.split(';')
                for part in parts:
                    if '=' in part:
                        key, value = part.strip().split('=', 1)
                        cookies[key.strip()] = value.strip()
            elif '=' in cookie_string:
                key, value = cookie_string.split('=', 1)
                cookies[key.strip()] = value.strip()
            else:
                # 如果是单个token，尝试不同的key名
                cookies['auth_token'] = cookie_string.strip()
                cookies['access_token'] = cookie_string.strip()
                cookies['session'] = cookie_string.strip()
        except:
            pass
            
        return cookies
    
    def _fetch_subscription_data(self) -> Optional[Dict]:
        """从API获取订阅数据"""
        try:
            # 尝试访问订阅API端点
            api_endpoints = [
                '/api/account/subscription',
                '/api/user/subscription',
                '/api/subscription',
                '/account/subscription'
            ]
            
            for endpoint in api_endpoints:
                try:
                    url = f"{self.base_url}{endpoint}"
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code == 200:
                        data = response.json()
                        return self._parse_api_response(data)
                        
                except (requests.RequestException, json.JSONDecodeError):
                    continue
                    
        except Exception:
            pass
            
        return None
    
    def _fetch_from_page(self) -> Optional[Dict]:
        """从页面HTML解析订阅信息"""
        try:
            # 访问订阅页面
            url = f"{self.base_url}/account/subscription"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                return self._parse_html_response(response.text)
                
        except Exception:
            pass
            
        return None
    
    def _parse_api_response(self, data: Dict) -> Dict:
        """解析API响应"""
        result = {}
        
        try:
            # 尝试解析不同的API响应格式
            if 'subscription' in data:
                sub_data = data['subscription']
            elif 'plan' in data:
                sub_data = data
            else:
                sub_data = data
            
            # 提取订阅信息
            result['plan_name'] = sub_data.get('plan_name', sub_data.get('plan', 'Unknown'))
            result['usage_count'] = sub_data.get('usage_count', sub_data.get('used', 0))
            result['usage_limit'] = sub_data.get('usage_limit', sub_data.get('limit', 0))
            result['remaining_count'] = result['usage_limit'] - result['usage_count']
            result['reset_date'] = sub_data.get('reset_date', sub_data.get('next_reset', ''))
            result['subscription_status'] = sub_data.get('status', 'active')
            
        except Exception:
            # 如果解析失败，返回默认值
            result = {
                'plan_name': 'Cookie登录',
                'usage_count': 0,
                'usage_limit': 100,  # 假设默认限制
                'remaining_count': 100,
                'reset_date': '未知',
                'subscription_status': 'active'
            }
        
        return result
    
    def _parse_html_response(self, html: str) -> Dict:
        """解析HTML页面"""
        result = {
            'plan_name': 'Cookie登录',
            'usage_count': 0,
            'usage_limit': 0,
            'remaining_count': 0,
            'reset_date': '未知',
            'subscription_status': 'active'
        }
        
        try:
            # 简单的HTML解析（可以使用BeautifulSoup进行更复杂的解析）
            import re
            
            # 查找使用次数相关信息
            usage_patterns = [
                r'usage["\']?\s*:\s*(\d+)',
                r'used["\']?\s*:\s*(\d+)',
                r'count["\']?\s*:\s*(\d+)',
                r'(\d+)\s*/\s*(\d+)\s*requests?',
                r'剩余\s*(\d+)\s*次',
                r'已使用\s*(\d+)\s*次'
            ]
            
            for pattern in usage_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                if matches:
                    if len(matches[0]) == 2:  # 格式如 "50/100"
                        result['usage_count'] = int(matches[0][0])
                        result['usage_limit'] = int(matches[0][1])
                    else:
                        result['usage_count'] = int(matches[0])
                    break
            
            # 计算剩余次数
            if result['usage_limit'] > 0:
                result['remaining_count'] = result['usage_limit'] - result['usage_count']
            
        except Exception:
            pass
        
        return result
    
    def format_subscription_info(self, sub_info: Dict) -> str:
        """格式化订阅信息为可读字符串"""
        if not sub_info.get('success'):
            return f"❌ 获取订阅信息失败: {sub_info.get('error', '未知错误')}"
        
        lines = []
        lines.append(f"📋 订阅计划: {sub_info['plan_name']}")
        
        if sub_info['usage_limit'] > 0:
            usage_percent = (sub_info['usage_count'] / sub_info['usage_limit']) * 100
            lines.append(f"📊 使用情况: {sub_info['usage_count']}/{sub_info['usage_limit']} ({usage_percent:.1f}%)")
            lines.append(f"🔢 剩余次数: {sub_info['remaining_count']} 次")
        else:
            lines.append(f"📊 已使用: {sub_info['usage_count']} 次")
        
        if sub_info['reset_date']:
            lines.append(f"🔄 重置日期: {sub_info['reset_date']}")
        
        lines.append(f"✅ 状态: {sub_info['subscription_status']}")
        
        return "\n".join(lines)


def get_subscription_checker() -> SubscriptionChecker:
    """获取订阅检查器实例"""
    return SubscriptionChecker()


def quick_check_subscription(cookie_string: str) -> Tuple[bool, Dict]:
    """
    快速检查订阅信息
    
    Args:
        cookie_string: Cookie字符串
        
    Returns:
        tuple: (是否成功, 订阅信息)
    """
    checker = get_subscription_checker()
    result = checker.get_subscription_info(cookie_string)
    return result.get('success', False), result
